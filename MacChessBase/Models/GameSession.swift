//
//  GameSession.swift
//  MacChessBase
//
//  Created on 2025/6/8.
//

import SwiftUI
import ChessKit
import Foundation



// MARK: - Game Status Enum
enum GameStatus: Equatable {
    case inProgress
    case checkmate(Piece.Color)
    case stalemate
    case draw(Board.EndResult.DrawType)
}

/// Represents a single chess game session with its state and metadata
@MainActor
class GameSession: ObservableObject, Identifiable {
    let id = UUID()
    
    // Core game state - now owned by GameSession
    @Published var game: Game
    @Published var board: Board
    @Published var currentMoveIndex: MoveTree.MoveIndex
    
    // Undo/Redo support
    weak var undoManager: UndoManager?
    
    // Session state
    @Published var name: String
    @Published var isActive: Bool = false
    @Published var isModified: Bool = false

    // UI state that should persist across session switches
    @Published var isBoardFlipped: Bool = false
    @Published var currentAnnotationColor: Move.VisualAnnotations.AnnotationColor = .green
    @Published var currentFilePath: URL?
    
    // Player names from PGN metadata - now reactive to game changes
    var whitePlayerName: String {
        game.tags.white.isEmpty ? "?" : game.tags.white
    }
    
    var blackPlayerName: String {
        game.tags.black.isEmpty ? "?" : game.tags.black
    }
    
    // Combined player display for UI
    var playersDisplayText: String {
        "\(whitePlayerName) - \(blackPlayerName)"
    }
    
    var gameStatus: GameStatus {
        // Calculate game status directly from board/game state
        if let lastMove = game.moves.getNodeMove(index: currentMoveIndex) {
            if let metaMove = lastMove.metaMove {
                switch metaMove.checkState {
                case .checkmate:
                    return .checkmate(board.position.sideToMove.opposite)
                case .stalemate:
                    return .stalemate
                default:
                    break
                }
            }
        }
        
        // Check for other draw conditions
        if board.position.clock.halfmoves >= 100 {
            return .draw(.fiftyMoves)
        } else if board.position.hasInsufficientMaterial {
            return .draw(.insufficientMaterial)
        }
        
        return .inProgress
    }
    
    var gameStatusText: String {
        switch gameStatus {
        case .inProgress:
            return "In Progress"
        case .checkmate(let winner):
            return "\(winner == .white ? "White" : "Black") Wins"
        case .stalemate:
            return "Stalemate"
        case .draw(let drawType):
            switch drawType {
            case .fiftyMoves:
                return "Draw (50 moves)"
            case .repetition:
                return "Draw (Repetition)"
            case .insufficientMaterial:
                return "Draw (Insufficient material)"
            case .stalemate:
                return "Stalemate"
            case .agreement:
                return "Draw (Agreement)"
            }
        }
    }
    
    var moveCount: Int {
        return game.moves.count
    }
    
    init(name: String = "") {
        // Initialize core game state
        let initialGame = Game()
        self.game = initialGame
        self.board = Board()
        self.currentMoveIndex = initialGame.startingIndex
        self.name = name
        
        // No ViewModel initialization needed - pure data model
    }
    
    // Removed: private var cancellables = Set<AnyCancellable>() - no longer needed
    
    // MARK: - Game Mutating Methods
    
    /// Create a new game in this session
    func newGame() {
        game = Game()
        board = Board()
        currentMoveIndex = game.startingIndex
        isModified = false

        // Trigger UI update
        objectWillChange.send()
    }
    
    /// Load a game from PGN
    func loadGame(from pgn: String) {
        guard let newGame = Game(pgn: pgn) else {
            print("Failed to parse PGN")
            return
        }
        
        self.game = newGame
        self.board = Board(position: newGame.startingPosition ?? .standard)
        self.currentMoveIndex = newGame.startingIndex
        
        // Trigger UI update
        objectWillChange.send()
    }
    
    /// Load a game from file
    func loadGame(from url: URL) {
        let fileManager = ChessFileManager.shared
        do {
            let gameObject = try fileManager.readGame(from: url)
            self.game = gameObject
            self.board = Board(position: gameObject.startingPosition ?? .standard)
            self.currentMoveIndex = gameObject.startingIndex
            
            // Trigger UI update
            objectWillChange.send()
        } catch {
            print("Error reading PGN file: \(error)")
        }
    }
    
    /// Load a game from a Game object
    func loadGameFromObject(_ gameObject: Game) {
        self.game = gameObject
        self.board = Board(position: gameObject.startingPosition ?? .standard)
        self.currentMoveIndex = gameObject.startingIndex
        isModified = true

        // Trigger UI update
        objectWillChange.send()
    }

    /// Import a game without undo/redo support (used for multi-game imports)
    func importGameWithoutUndo(_ gameObject: Game) {
        loadGameFromObject(gameObject)
    }

    /// Load a game from a Game object with undo support
    func loadGameFromObjectWithUndo(_ gameObject: Game) -> ImportGameUndoState? {
        // Check if PGN actually changed
        let currentPGN = game.pgn
        let newPGN = gameObject.pgn

        if currentPGN == newPGN {
            // No change, don't register undo
            return nil
        }
        print("currentPGN: \(currentPGN)")
        print("newPGN: \(newPGN)")

        // Create undo state before making changes
        let undoState = ImportGameUndoState(
            originalGame: game,
            originalCurrentMoveIndex: currentMoveIndex,
            originalBoard: board
        )

        // Apply the new game
        loadGameFromObject(gameObject)

        return undoState
    }
    
    /// Load a position from FEN string
    func loadPosition(from fen: String) {
        guard let position = FENParser.parse(fen: fen) else {
            print("Error: Invalid FEN format")
            return
        }

        // Create a new game with the FEN position as starting position
        var newGame = Game(startingWith: position)

        // If the position is not the standard starting position, set FEN and SetUp tags
        if position != .standard {
            newGame.tags.fen = fen
            newGame.tags.setUp = "1"
        }

        loadGameFromObject(newGame)
        print("Position loaded from FEN: \(fen)")
    }

    /// Load a position from FEN string with undo support
    func loadPositionWithUndo(from fen: String) -> ImportGameUndoState? {
        guard let position = FENParser.parse(fen: fen) else {
            print("Error: Invalid FEN format")
            return nil
        }

        // Create a new game with the FEN position as starting position
        var newGame = Game(startingWith: position)

        // If the position is not the standard starting position, set FEN and SetUp tags
        if position != .standard {
            newGame.tags.fen = fen
            newGame.tags.setUp = "1"
        }

        // Use the undo-enabled method
        let undoState = loadGameFromObjectWithUndo(newGame)
        if undoState != nil {
            print("Position loaded from FEN with undo support: \(fen)")
        }

        return undoState
    }
    
    /// Make a move in the game with undo support
    func makeMove(from startSquare: Square, to endSquare: Square) -> Bool {
        // Execute the move on the board first
        guard let executedMove = board.move(pieceAt: startSquare, to: endSquare) else {
            return false
        }

        // Add the move to the game tree with undo support
        let (newIndex, undoState) = game.makeWithUndoAndPromotion(move: executedMove, from: currentMoveIndex)
        currentMoveIndex = newIndex
        isModified = true

        // Trigger UI update
        objectWillChange.send()
        
        // Register undo operation
        if let undoState {
            undoManager?.registerUndo(withTarget: self) { target in
                MainActor.assumeIsolated {
                    _ = target.game.undoMake(undoState)
                    target.currentMoveIndex = undoState.resolvedParentIndex(in: target.game.moves) ?? undoState.parentIndex
                    target.updateBoardToCurrentIndex()
                    target.objectWillChange.send()
                    
                    // Register redo operation
                    target.undoManager?.registerUndo(withTarget: target) { redoTarget in
                        MainActor.assumeIsolated {
                            _ = redoTarget.makeMove(from: startSquare, to: endSquare)
                        }
                    }
                }
            }
            undoManager?.setActionName("Move")
        }
        
        return true
    }

    /// Make a move in the game with promotion and undo support
    func makeMoveWithPromotion(from startSquare: Square, to endSquare: Square, promotionPiece: Piece.Kind) -> Bool {
        // Execute the move on the board first with promotion
        guard let executedMove = board.moveWithPromotion(pieceAt: startSquare, to: endSquare, promotionPiece: promotionPiece) else {
            return false
        }

        // Add the move to the game tree with undo support
        let (newIndex, undoState) = game.makeWithUndoAndPromotion(move: executedMove, from: currentMoveIndex, promotionPiece: promotionPiece)
        currentMoveIndex = newIndex
        isModified = true

        // Trigger UI update
        objectWillChange.send()

        // Register undo operation
        if let undoState {
            undoManager?.registerUndo(withTarget: self) { target in
                MainActor.assumeIsolated {
                    _ = target.game.undoMake(undoState)
                    target.currentMoveIndex = undoState.resolvedParentIndex(in: target.game.moves) ?? undoState.parentIndex
                    target.updateBoardToCurrentIndex()
                    target.objectWillChange.send()

                    // Register redo operation
                    target.undoManager?.registerUndo(withTarget: target) { redoTarget in
                        MainActor.assumeIsolated {
                            _ = redoTarget.makeMoveWithPromotion(from: startSquare, to: endSquare, promotionPiece: promotionPiece)
                        }
                    }
                }
            }
            undoManager?.setActionName("Move")
        }

        return true
    }

    /// Delete a move and all subsequent moves with undo support
    func deleteMove(at index: MoveTree.MoveIndex) -> Bool {
        let nextIndex = game.moves.previousIndex(currentIndex: index) ?? game.startingIndex
        let previousCurrentIndex = currentMoveIndex
        
        let (success, undoState) = game.deleteWithUndo(at: index)
        guard success else {
            print("Failed to delete move at index: \(index)")
            return false
        }
        
        currentMoveIndex = nextIndex
        updateBoardToCurrentIndex()
        objectWillChange.send()
        
        // Register undo operation
        if let undoState {
            undoManager?.registerUndo(withTarget: self) { target in
                MainActor.assumeIsolated {
                    _ = target.game.undoDelete(undoState)
                    target.currentMoveIndex = previousCurrentIndex
                    target.updateBoardToCurrentIndex()
                    target.objectWillChange.send()
                    
                    // Register redo operation
                    target.undoManager?.registerUndo(withTarget: target) { redoTarget in
                        MainActor.assumeIsolated {
                            _ = redoTarget.deleteMove(at: index)
                        }
                    }
                }
            }
            undoManager?.setActionName("Delete Move")
        }
        
        return true
    }
    
    /// Delete all moves before the specified move with undo support
    func deleteBeforeMove(at index: MoveTree.MoveIndex) -> Bool {
        guard index != MoveTree.minimumIndex else {
            print("Cannot delete before the head node")
            return false
        }
        
        let previousCurrentIndex = currentMoveIndex
        
        let (success, undoState) = game.deleteBeforeMoveWithUndo(at: index)
        guard success else {
            print("Failed to delete before move at index: \(index)")
            return false
        }
        
        currentMoveIndex = index
        updateBoardToCurrentIndex()
        objectWillChange.send()
        
        // Register undo operation
        if let undoState {
            undoManager?.registerUndo(withTarget: self) { target in
                MainActor.assumeIsolated {
                    _ = target.game.undoDeleteBeforeMove(undoState)
                    target.currentMoveIndex = previousCurrentIndex
                    target.updateBoardToCurrentIndex()
                    target.objectWillChange.send()
                    
                    // Register redo operation
                    target.undoManager?.registerUndo(withTarget: target) { redoTarget in
                        MainActor.assumeIsolated {
                            _ = target.deleteBeforeMove(at: index)
                        }
                    }
                }
            }
            undoManager?.setActionName("Delete Before Move")
        }
        
        return true
    }
    
    /// Promote a variation's priority with undo support
    func promoteVariation(at index: MoveTree.MoveIndex) -> Bool {
        let (success, undoState) = game.promoteWithUndo(index: index)
        guard success else {
            print("Failed to promote variation priority at index: \(index)")
            return false
        }

        isModified = true
        updateBoardToCurrentIndex()
        objectWillChange.send()
        
        // Register undo operation
        if let undoState {
            undoManager?.registerUndo(withTarget: self) { target in
                MainActor.assumeIsolated {
                    _ = target.game.undoPromote(undoState)
                    target.updateBoardToCurrentIndex()
                    target.objectWillChange.send()
                    
                    // Register redo operation
                    target.undoManager?.registerUndo(withTarget: target) { redoTarget in
                        MainActor.assumeIsolated {
                            _ = redoTarget.promoteVariation(at: index)
                        }
                    }
                }
            }
            undoManager?.setActionName("Promote Variation")
        }
        
        return true
    }
    
    /// Promote a variation to main variation with undo support
    func promoteToMainVariation(at index: MoveTree.MoveIndex) -> Bool {
        let (success, undoState) = game.promoteToMainVariationWithUndo(index: index)
        guard success else {
            print("Failed to promote to main variation at index: \(index)")
            return false
        }

        isModified = true
        updateBoardToCurrentIndex()
        objectWillChange.send()
        
        // Register undo operation
        if let undoState {
            undoManager?.registerUndo(withTarget: self) { target in
                MainActor.assumeIsolated {
                    _ = target.game.undoPromoteToMain(undoState)
                    target.updateBoardToCurrentIndex()
                    target.objectWillChange.send()
                    
                    // Register redo operation
                    target.undoManager?.registerUndo(withTarget: target) { redoTarget in
                        MainActor.assumeIsolated {
                            _ = redoTarget.promoteToMainVariation(at: index)
                        }
                    }
                }
            }
            undoManager?.setActionName("Promote to Main")
        }
        
        return true
    }
    
    /// Overwrite moves starting from a position with undo support
    func overwriteMove(_ move: Move, from index: MoveTree.MoveIndex) -> MoveTree.MoveIndex {
        let previousCurrentIndex = currentMoveIndex
        let (newIndex, undoState) = game.overwriteWithUndoAndPromotion(move: move, from: index)
        currentMoveIndex = newIndex
        isModified = true
        updateBoardToCurrentIndex()
        objectWillChange.send()
        
        // Register undo operation
        if let undoState {
            undoManager?.registerUndo(withTarget: self) { target in
                MainActor.assumeIsolated {
                    _ = target.game.undoOverwrite(undoState)
                    target.currentMoveIndex = previousCurrentIndex
                    target.updateBoardToCurrentIndex()
                    target.objectWillChange.send()
                    
                    // Register redo operation
                    target.undoManager?.registerUndo(withTarget: target) { redoTarget in
                        MainActor.assumeIsolated {
                            _ = redoTarget.overwriteMove(move, from: index)
                        }
                    }
                }
            }
            undoManager?.setActionName("Overwrite Move")
        }
        
        return newIndex
    }
    
    /// Navigate to a specific move index
    func goToMove(at index: MoveTree.MoveIndex) {
        if game.positions[index] != nil || index == game.startingIndex {
            currentMoveIndex = index
            updateBoardToCurrentIndex()
            objectWillChange.send()
        }
    }
    
    /// Navigate to the previous move
    func goToPreviousMove() {
        let history = game.moves.history(for: currentMoveIndex)
        
        if history.isEmpty || currentMoveIndex == game.startingIndex {
            return
        }
        
        let targetIndex: MoveTree.MoveIndex
        if history.count == 1 {
            targetIndex = game.startingIndex
        } else {
            targetIndex = history[history.count - 2]
        }
        
        if game.positions[targetIndex] != nil || targetIndex == game.startingIndex {
            currentMoveIndex = targetIndex
            updateBoardToCurrentIndex()
            objectWillChange.send()
        }
    }
    
    /// Navigate to the next move - returns available move indices
    func goToNextMove() -> [MoveTree.MoveIndex] {
        guard let mainLineNext = game.moves.nextIndex(currentIndex: currentMoveIndex) else {
            return []
        }
        
        let variations = game.moves.variations(from: currentMoveIndex)
        return [mainLineNext] + variations
    }
    
    /// Navigate to start of the game
    func goToStart() {
        currentMoveIndex = game.startingIndex
        updateBoardToCurrentIndex()
        objectWillChange.send()
    }
    
    /// Navigate to end of the game
    func goToEnd() {
        var targetIndex = currentMoveIndex
        var nextIndex = game.moves.nextIndex(currentIndex: currentMoveIndex)
        
        while nextIndex != nil {
            targetIndex = nextIndex!
            nextIndex = game.moves.nextIndex(currentIndex: nextIndex!)
        }
        
        currentMoveIndex = targetIndex
        updateBoardToCurrentIndex()
        objectWillChange.send()
    }
    
    /// Update board position to match current move index
    private func updateBoardToCurrentIndex() {
        if let position = game.positions[currentMoveIndex] {
            board = Board(position: position)
        } else if currentMoveIndex == game.startingIndex {
            board = Board(position: game.startingPosition ?? .standard)
        }
    }
    
    // MARK: - Undo/Redo Public Interface
    
    /// Undo the last operation
    func undo() {
        if undoManager?.canUndo == true {
            undoManager?.undo()
            objectWillChange.send()
        }
    }
    
    /// Redo the last undone operation
    func redo() {
        if undoManager?.canRedo == true {
            undoManager?.redo()
            objectWillChange.send()
        }
    }
    
    /// Check if undo is available
    var canUndo: Bool {
        undoManager?.canUndo ?? false
    }
    
    /// Check if redo is available
    var canRedo: Bool {
        undoManager?.canRedo ?? false
    }
    
    /// Get the name of the next undo action
    var undoActionName: String {
        undoManager?.undoActionName ?? ""
    }
    
    /// Get the name of the next redo action
    var redoActionName: String {
        undoManager?.redoActionName ?? ""
    }
    
    // MARK: - Move Editing with Undo Support
    
    /// Edit a move with undo support
    func editMove(at index: MoveTree.MoveIndex, newMove: Move) -> Bool {
        guard let undoState = game.editMoveWithUndo(index: index, newMove: newMove) else {
            return false
        }

        isModified = true

        // Update board and notify changes
        updateBoardToCurrentIndex()
        objectWillChange.send()
        
        // Register undo operation
        undoManager?.registerUndo(withTarget: self) { target in
            MainActor.assumeIsolated {
                _ = target.game.undoEditMove(undoState)
                target.updateBoardToCurrentIndex()
                target.objectWillChange.send()
                
                // Register redo operation
                target.undoManager?.registerUndo(withTarget: target) { redoTarget in
                    MainActor.assumeIsolated {
                        _ = redoTarget.editMove(at: index, newMove: newMove)
                    }
                }
            }
        }
        undoManager?.setActionName("Edit Move")
        
        return true
    }
    
    /// Make a move (called from ViewModel) - specialized version
    func makeMoveInGame(_ move: Move, from index: MoveTree.MoveIndex) -> MoveTree.MoveIndex {
        let (newIndex, undoState) = game.makeWithUndoAndPromotion(move: move, from: index)
        currentMoveIndex = newIndex
        isModified = true

        // Update board and notify
        updateBoardToCurrentIndex()
        objectWillChange.send()
        
        // Register undo operation
        if let undoState {
            undoManager?.registerUndo(withTarget: self) { target in
                MainActor.assumeIsolated {
                    _ = target.game.undoMake(undoState)
                    target.currentMoveIndex = undoState.resolvedParentIndex(in: target.game.moves) ?? undoState.parentIndex
                    target.updateBoardToCurrentIndex()
                    target.objectWillChange.send()
                    
                    // Register redo operation
                    target.undoManager?.registerUndo(withTarget: target) { redoTarget in
                        MainActor.assumeIsolated {
                            let redoTargetIndex = undoState.resolvedParentIndex(in: redoTarget.game.moves) ?? undoState.parentIndex
                            _ = redoTarget.makeMoveInGame(move, from: redoTargetIndex)
                        }
                    }
                }
            }
            undoManager?.setActionName("Move")
        }
        
        return newIndex
    }

    /// Undoes an import game operation
    func undoImportGame(_ undoState: ImportGameUndoState) -> Bool {
        // Restore the original game state
        self.game = undoState.originalGame
        self.board = undoState.originalBoard
        self.currentMoveIndex = undoState.originalCurrentMoveIndex

        // Notify viewModel to update its derived properties
        objectWillChange.send()

        return true
    }

    /// Load a game from clipboard with undo support
    func loadGameFromClipboard() -> Bool {
        let fileManager = ChessFileManager.shared
        guard let game = fileManager.readGameFromClipboard() else {
            return false
        }
        
        // Use undo-enabled method to load the game
        if let undoState = loadGameFromObjectWithUndo(game) {
            // Register undo operation
            undoManager?.registerUndo(withTarget: self) { target in
                MainActor.assumeIsolated {
                    _ = target.undoImportGame(undoState)

                    // Register redo operation
                    target.undoManager?.registerUndo(withTarget: target) { redoTarget in
                        MainActor.assumeIsolated {
                            _ = redoTarget.loadGameFromClipboard()
                        }
                    }
                }
            }
            undoManager?.setActionName("Import from Clipboard")
        }
        
        return true
    }

    /// Set a position from the position editor with undo support
    func setPosition(_ position: Position, shouldFlip: Bool = false) -> Bool {
        // Create a new game with the custom position as starting position
        var newGame = Game(startingWith: position)

        // If the position is not the standard starting position, set FEN and SetUp tags
        if position != .standard {
            newGame.tags.fen = position.fen
            newGame.tags.setUp = "1"
        }

        isModified = true

        // Use undo-enabled method to load the game
        if let undoState = loadGameFromObjectWithUndo(newGame) {
            // Register undo operation
            undoManager?.registerUndo(withTarget: self) { target in
                MainActor.assumeIsolated {
                    _ = target.undoImportGame(undoState)

                    // Register redo operation
                    target.undoManager?.registerUndo(withTarget: target) { redoTarget in
                        MainActor.assumeIsolated {
                            _ = redoTarget.setPosition(position, shouldFlip: shouldFlip)
                        }
                    }
                }
            }
            undoManager?.setActionName("Set Position")
        }

        // Update board flip state if requested
        if shouldFlip != isBoardFlipped {
            isBoardFlipped = shouldFlip
        }

        return true
    }

    // MARK: - Computed Properties
    
    /// Gets the game's PGN representation
    var pgn: String {
        game.pgn
    }
    
    /// Gets the current position
    var currentPosition: Position? {
        game.positions[currentMoveIndex]
    }
    
    /// Checks if we can go to the previous move
    var canGoToPreviousMove: Bool {
        let history = game.moves.history(for: currentMoveIndex)
        return !history.isEmpty && currentMoveIndex != game.startingIndex
    }
    
    /// Checks if we can go to the next move
    var canGoToNextMove: Bool {
        return game.moves.nextIndex(currentIndex: currentMoveIndex) != nil
    }


}
