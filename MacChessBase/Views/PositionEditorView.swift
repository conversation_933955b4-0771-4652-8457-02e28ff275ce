//
//  PositionEditorView.swift
//  MacChessBase
//
//  Created by AI Assistant on 2025/7/7.
//

import SwiftUI
import ChessKit
import AppKit //

/// A view for editing chess positions
struct PositionEditorView: View {
    @StateObject private var viewModel: PositionEditorViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var showInvalidPositionAlert = false

    private let boardSize: CGFloat = 600
    private let pieceSelectionWidth: CGFloat = 140
    private let optionsWidth: CGFloat = 200

    var onPositionSet: ((Position, Bool) -> Void)?

    // MARK: - Initializers

    /// Initialize with standard starting position
    init(onPositionSet: ((Position, Bool) -> Void)? = nil) {
        self._viewModel = StateObject(wrappedValue: PositionEditorViewModel())
        self.onPositionSet = onPositionSet
    }

    /// Initialize with a specific FEN position
    init(initialFEN: String, onPositionSet: ((Position, Bool) -> Void)? = nil) {
        self._viewModel = StateObject(wrappedValue: PositionEditorViewModel(fen: initialFEN))
        self.onPositionSet = onPositionSet
    }

    /// Initialize with a specific FEN position and board flip state
    init(initialFEN: String, isBoardFlipped: Bool, onPositionSet: ((Position, Bool) -> Void)? = nil) {
        self._viewModel = StateObject(wrappedValue: PositionEditorViewModel(fen: initialFEN, isBoardFlipped: isBoardFlipped))
        self.onPositionSet = onPositionSet
    }

    var body: some View {
        VStack(spacing: 20) {
            HStack(alignment: .top, spacing: 20) {
                editableBoardView
                    .frame(width: boardSize, height: boardSize)

                pieceSelectionView
                    .frame(width: pieceSelectionWidth, height: boardSize)

                optionsView1
                    .frame(width: optionsWidth, height: boardSize)
            }

            optionsView2
        }
        .padding(20)
        .background(Color(NSColor.windowBackgroundColor))
        .frame(width: boardSize + pieceSelectionWidth + optionsWidth + 80)
        .alert("Invalid Position!", isPresented: $showInvalidPositionAlert) {
            Button("OK", role: .cancel) { }
        } message: {
            Text("The current position is not valid. Please check the position and try again.")
        }
    }

    // MARK: - Editable Board View

    private var editableBoardView: some View {
        let squareSize = boardSize / 8
        let transformer = BoardCoordinateTransformer(isFlipped: viewModel.isBoardFlipped)

        return ZStack {
            VStack(spacing: 0) {
                ForEach(transformer.rankLabels, id: \.self) { rank in
                    HStack(spacing: 0) {
                        ForEach(transformer.fileLabels, id: \.self) { file in
                            let square = Square("\(file)\(rank)")

                            editableSquareView(for: square, squareSize: squareSize)
                        }
                    }
                }
            }

            coordinateLabelsView(squareSize: squareSize)
        }
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }

    private func editableSquareView(for square: Square, squareSize: CGFloat) -> some View {
        let isLightSquare = (square.file.number + square.rank.value) % 2 == 1
        let backgroundColor = isLightSquare ? Color.brown.opacity(0.3) : Color.brown.opacity(0.7)

        return ZStack {
            Rectangle()
                .fill(backgroundColor)

            if let piece = viewModel.position.piece(at: square) {
                Image(pieceImageName(for: piece))
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .padding(4)
            }

            RightClickGestureView {
                viewModel.handleRightClick(square)
            }
            .frame(width: squareSize, height: squareSize)
            .background(Color.clear)
            .allowsHitTesting(true)
        }
        .frame(width: squareSize, height: squareSize)
        .border(Color.gray.opacity(0.3), width: 0.5)
        .onTapGesture {
            viewModel.handleSquareTap(square)
        }
    }

    private func coordinateLabelsView(squareSize: CGFloat) -> some View {
        ZStack {
            let rankArray = viewModel.isBoardFlipped ? [1, 2, 3, 4, 5, 6, 7, 8] : [8, 7, 6, 5, 4, 3, 2, 1]
            VStack(spacing: 0) {
                ForEach(rankArray, id: \.self) { rank in
                    HStack {
                        Text("\(rank)")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                        Spacer()
                    }
                    .frame(height: squareSize)
                }
            }
            .padding(.leading, 2)

            VStack {
                Spacer()
                HStack(spacing: 0) {
                    ForEach(viewModel.isBoardFlipped ? ["h", "g", "f", "e", "d", "c", "b", "a"] : ["a", "b", "c", "d", "e", "f", "g", "h"], id: \.self) { file in
                        VStack {
                            Spacer()
                            Text(file)
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                        .frame(width: squareSize)
                    }
                }
                .padding(.bottom, 0)
            }
        }
    }

    // MARK: - Piece Selection View

    private var pieceSelectionView: some View {
        VStack(spacing: 8) {
            Text("Pieces")
                .font(.headline)
                .padding(.bottom, 8)

            VStack(spacing: 4) {
                Text("White")
                    .font(.caption)
                    .foregroundColor(.secondary)

                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 4) {
                    ForEach(Piece.Kind.allCases.reversed(), id: \.self) { kind in
                        pieceButton(kind: kind, color: .white)
                    }
                }
            }

            Divider()

            VStack(spacing: 4) {
                Text("Black")
                    .font(.caption)
                    .foregroundColor(.secondary)

                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 4) {
                    ForEach(Piece.Kind.allCases.reversed(), id: \.self) { kind in
                        pieceButton(kind: kind, color: .black)
                    }
                }
            }

            Divider()

            Button(action: {
                viewModel.selectedTool = .delete
            }) {
                VStack {
                    Image(systemName: "trash")
                        .font(.title2)
                    Text("Delete")
                        .font(.caption)
                }
                .frame(maxWidth: .infinity)
                .padding(8)
                .background(viewModel.selectedTool == .delete ? Color.accentColor.opacity(0.3) : Color.clear)
                .cornerRadius(4)
            }
            .buttonStyle(PlainButtonStyle())

            Divider()

            HStack(spacing: 4) {
                Button(action: {
                    captureBoardToClipboard()
                }) {
                    VStack {
                        Image(systemName: "camera")
                            .font(.title3)
                        Text("Copy Screenshot")
                            .font(.caption2)
                            .multilineTextAlignment(.center)
                            .lineLimit(nil)
                    }
                    .frame(maxWidth: .infinity, minHeight: 50)
                    .padding(4)
                    .cornerRadius(4)
                }
                .buttonStyle(PlainButtonStyle())

                Button(action: {
                    saveBoardScreenshot()
                }) {
                    VStack {
                        Image(systemName: "square.and.arrow.down")
                            .font(.title3)
                        Text("Save Screenshot")
                            .font(.caption2)
                            .multilineTextAlignment(.center)
                            .lineLimit(nil)
                    }
                    .frame(maxWidth: .infinity, minHeight: 50)
                    .padding(4)
                    .cornerRadius(4)
                }
                .buttonStyle(PlainButtonStyle())
            }

            Spacer()
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
        .cornerRadius(8)
    }

    private func pieceButton(kind: Piece.Kind, color: Piece.Color) -> some View {
        let piece = Piece(kind, color: color, square: .a1)
        let isSelected = viewModel.selectedTool == .piece(piece)

        return Button(action: {
            viewModel.selectedTool = .piece(piece)
        }) {
            Image(pieceImageName(for: piece))
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 50, height: 50)
                .background(isSelected ? Color.accentColor.opacity(0.3) : Color.clear)
                .cornerRadius(4)
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Options View 1

    private var optionsView1: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Position Settings")
                .font(.headline)

            GroupBox(label: Text("Castling Rights")) {
                VStack(alignment: .leading, spacing: 4) {
                    Toggle("White King-side", isOn: $viewModel.whiteCanCastleKingSide)
                    Toggle("White Queen-side", isOn: $viewModel.whiteCanCastleQueenSide)
                    Toggle("Black King-side", isOn: $viewModel.blackCanCastleKingSide)
                    Toggle("Black Queen-side", isOn: $viewModel.blackCanCastleQueenSide)
                }
                .font(.caption)
            }

            GroupBox(label: Text("Side to Move")) {
                Picker("", selection: $viewModel.sideToMove) {
                    Text("White").tag(Piece.Color.white)
                    Text("Black").tag(Piece.Color.black)
                }
                .pickerStyle(SegmentedPickerStyle())
            }

            GroupBox(label: Text("Initial Move Number")) {
                HStack {
                    Button("-") {
                        if viewModel.initialNumber > 1 {
                            viewModel.initialNumber -= 1
                        }
                    }
                    .disabled(viewModel.initialNumber <= 1)

                    TextField("", value: $viewModel.initialNumber, format: .number)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .frame(width: 60)
                        .multilineTextAlignment(.center)
                        .onSubmit {
                            if viewModel.initialNumber < 1 {
                                viewModel.initialNumber = 1
                            }
                        }

                    Button("+") {
                        viewModel.initialNumber += 1
                    }
                }
                .font(.caption)
            }

            GroupBox(label: Text("Board Orientation")) {
                Toggle("Flip Board", isOn: $viewModel.isBoardFlipped)
                    .font(.caption)
            }

            GroupBox(label: Text("Clipboard")) {
                VStack(spacing: 4) {
                    Button("Copy FEN") {
                        viewModel.copyFENToClipboard()
                    }
                    Button("Paste FEN") {
                        viewModel.pasteFENFromClipboard()
                    }
                }
                .font(.caption)
            }

            GroupBox(label: fenLabel) {
                VStack(alignment: .leading, spacing: 4) {
                    TextEditor(text: $viewModel.fenString)
                        .font(.system(.caption, design: .monospaced))
                        .frame(minHeight: 50, maxHeight: 80)
                        .border(Color.gray.opacity(0.3), width: 1)
                        .cornerRadius(4)

                    Button("Load FEN") {
                        viewModel.loadFromFEN()
                    }
                    .font(.caption)
                }
            }

            Spacer()
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
        .cornerRadius(8)
    }

    private var optionsView2: some View {
        HStack(spacing: 16) {
            Button("OK") {
                if viewModel.validateCurrentFEN() {
                    if let position = viewModel.currentPosition {
                        onPositionSet?(position, viewModel.isBoardFlipped)
                    }
                    dismiss()
                } else {
                    showInvalidPositionAlert = true
                }
            }
            .keyboardShortcut(.return)

            Button("Reset") {
                viewModel.resetToStandardPosition()
            }

            Button("Clear") {
                viewModel.clearBoard()
            }

            Button("Cancel") {
                dismiss()
            }
            .keyboardShortcut(.escape)
        }
        .padding()
    }

    private var fenLabel: some View {
        HStack(spacing: 4) {
            Text("FEN")
            if !viewModel.isCurrentFENValid {
                Text("(Invalid)").foregroundColor(.red.opacity(0.8))
            }
        }
    }

    private func pieceImageName(for piece: Piece) -> String {
        let colorPrefix = piece.color == .white ? "w" : "b"
        let pieceSymbol: String

        switch piece.kind {
        case .pawn: pieceSymbol = "P"
        case .knight: pieceSymbol = "N"
        case .bishop: pieceSymbol = "B"
        case .rook: pieceSymbol = "R"
        case .queen: pieceSymbol = "Q"
        case .king: pieceSymbol = "K"
        }

        return "\(colorPrefix)\(pieceSymbol)"
    }
    
    // MARK: - Screenshot Methods
    
    private func captureBoardToClipboard() {
        Task { @MainActor in
            guard let image = captureChessBoard() else {
                print("Failed to capture chess board image")
                return
            }
            
            copyImageToClipboard(image)
        }
    }
    
    private func saveBoardScreenshot() {
        Task { @MainActor in
            guard let image = captureChessBoard() else {
                print("Failed to capture chess board image")
                return
            }
            
            saveImageToFile(image)
        }
    }
    
    @MainActor
    private func captureChessBoard() -> NSImage? {
        let screenshotView = createBoardForScreenshot()
        
        let screenshotWithBackground = VStack {
            screenshotView
        }
        .background(
            Rectangle()
                .fill(Color(.white.opacity(0.9)))
        )
        .padding(10)
        
        let renderer = ImageRenderer(content: screenshotWithBackground)
        renderer.scale = 2.0
        
        return renderer.nsImage
    }
    
    private func createBoardForScreenshot() -> some View {
        let squareSize = boardSize / 8
        let transformer = BoardCoordinateTransformer(isFlipped: viewModel.isBoardFlipped)
        
        return VStack(spacing: 0) {
            ForEach(transformer.rankLabels, id: \.self) { rank in
                HStack(spacing: 0) {
                    ForEach(transformer.fileLabels, id: \.self) { file in
                        let square = Square("\(file)\(rank)")
                        let isLightSquare = (square.file.number + square.rank.value) % 2 == 1
                        let backgroundColor = isLightSquare ? Color.brown.opacity(0.3) : Color.brown.opacity(0.7)
                        
                        ZStack {
                            Rectangle()
                                .fill(backgroundColor)
                            
                            if let piece = viewModel.position.piece(at: square) {
                                Image(pieceImageName(for: piece))
                                    .resizable()
                                    .aspectRatio(contentMode: .fit)
                                    .padding(4)
                            }
                        }
                        .frame(width: squareSize, height: squareSize)
                    }
                }
            }
        }
    }
    
    private func copyImageToClipboard(_ image: NSImage) {
        let pasteboard = NSPasteboard.general
        pasteboard.clearContents()
        pasteboard.writeObjects([image])
        
        print("Position editor chess board screenshot copied to clipboard")
    }
    
    private func saveImageToFile(_ image: NSImage) {
        let savePanel = NSSavePanel()
        savePanel.allowedContentTypes = [.png, .jpeg]
        
        let timestamp = Int(Date().timeIntervalSince1970)
        savePanel.nameFieldStringValue = "PositionEditor_\(timestamp).png"
        savePanel.title = "Save Position Editor Screenshot"
        savePanel.message = "Choose a location to save the position editor screenshot"
        
        savePanel.begin { response in
            guard response == .OK, let url = savePanel.url else { return }
            
            guard let tiffData = image.tiffRepresentation,
                  let bitmapImage = NSBitmapImageRep(data: tiffData),
                  let pngData = bitmapImage.representation(using: .png, properties: [:]) else {
                print("Failed to convert image to PNG data")
                return
            }
            
            do {
                try pngData.write(to: url)
                print("Position editor screenshot saved to: \(url.path)")
            } catch {
                print("Failed to save screenshot: \(error.localizedDescription)")
            }
        }
    }
}

// MARK: - Right Click Recognizer View

struct RightClickGestureView: NSViewRepresentable {
    let onRightClick: () -> Void

    func makeNSView(context: Context) -> NSView {
        let view = NSView()
        let rightClick = NSClickGestureRecognizer(target: context.coordinator, action: #selector(Coordinator.handleRightClick(_:)))
        rightClick.buttonMask = 0x2 // Right mouse button
        view.addGestureRecognizer(rightClick)
        return view
    }

    func updateNSView(_ nsView: NSView, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(onRightClick: onRightClick)
    }

    class Coordinator: NSObject {
        let onRightClick: () -> Void

        init(onRightClick: @escaping () -> Void) {
            self.onRightClick = onRightClick
        }

        @objc func handleRightClick(_ sender: NSClickGestureRecognizer) {
            onRightClick()
        }
    }
}
