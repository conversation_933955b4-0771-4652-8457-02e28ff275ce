//
//  PositionEditorView.swift
//  MacChessBase
//
//  Created by AI Assistant on 2025/7/7.
//

import SwiftUI
import ChessKit
import AppKit //

/// A view for editing chess positions
struct PositionEditorView: View {
    @StateObject private var viewModel: PositionEditorViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var showInvalidPositionAlert = false

    private let boardSize: CGFloat = 600
    private let pieceSelectionWidth: CGFloat = 140
    private let optionsWidth: CGFloat = 200

    var onPositionSet: ((Position, Bool) -> Void)?

    // MARK: - Initializers

    /// Initialize with standard starting position
    init(onPositionSet: ((Position, Bool) -> Void)? = nil) {
        self._viewModel = StateObject(wrappedValue: PositionEditorViewModel())
        self.onPositionSet = onPositionSet
    }

    /// Initialize with a specific FEN position
    init(initialFEN: String, onPositionSet: ((Position, Bool) -> Void)? = nil) {
        self._viewModel = StateObject(wrappedValue: PositionEditorViewModel(fen: initialFEN))
        self.onPositionSet = onPositionSet
    }

    /// Initialize with a specific FEN position and board flip state
    init(initialFEN: String, isBoardFlipped: Bool, onPositionSet: ((Position, Bool) -> Void)? = nil) {
        self._viewModel = StateObject(wrappedValue: PositionEditorViewModel(fen: initialFEN, isBoardFlipped: isBoardFlipped))
        self.onPositionSet = onPositionSet
    }

    var body: some View {
        VStack(spacing: 20) {
            HStack(alignment: .top, spacing: 20) {
                editableBoardView
                    .frame(width: boardSize, height: boardSize)

                pieceSelectionView
                    .frame(width: pieceSelectionWidth, height: boardSize)

                optionsView1
                    .frame(width: optionsWidth, height: boardSize)
            }

            optionsView2
        }
        .padding(20)
        .background(Color(NSColor.windowBackgroundColor))
        .frame(width: boardSize + pieceSelectionWidth + optionsWidth + 80)
        .alert("Invalid Position!", isPresented: $showInvalidPositionAlert) {
            Button("OK", role: .cancel) { }
        } message: {
            Text("The current position is not valid. Please check the position and try again.")
        }
    }

    // MARK: - Editable Board View

    private var editableBoardView: some View {
        let squareSize = boardSize / 8

        return ZStack {
            ZStack {
                Canvas { context, size in
                    drawChessboard(in: context, size: size)
                    drawPieces(in: context, size: size)
                }

                // Overlay for both left and right click detection
                InteractiveOverlayView { location in
                    handleTap(at: location, size: CGSize(width: boardSize, height: boardSize))
                } onRightClick: { location in
                    handleRightClick(at: location, size: CGSize(width: boardSize, height: boardSize))
                }
                .frame(width: boardSize, height: boardSize)
                .allowsHitTesting(true)
            }

            coordinateLabelsView(squareSize: squareSize)
        }
        .background(Color.gray.opacity(0.1))
        .cornerRadius(8)
    }

    // MARK: - Canvas Drawing Methods

    private func drawChessboard(in context: GraphicsContext, size: CGSize) {
        let squareSize = size.width / 8.0
        let transformer = BoardCoordinateTransformer(isFlipped: viewModel.isBoardFlipped)

        for (rankIndex, rank) in transformer.rankLabels.enumerated() {
            for (fileIndex, file) in transformer.fileLabels.enumerated() {
                let square = Square("\(file)\(rank)")
                let isLightSquare = (square.file.number + square.rank.value) % 2 == 1
                let backgroundColor = isLightSquare ? Color.brown.opacity(0.3) : Color.brown.opacity(0.7)

                let rect = CGRect(
                    x: CGFloat(fileIndex) * squareSize,
                    y: CGFloat(rankIndex) * squareSize,
                    width: squareSize,
                    height: squareSize
                )

                context.fill(Path(rect), with: .color(backgroundColor))

                // Draw border
                context.stroke(Path(rect), with: .color(Color.gray.opacity(0.3)), lineWidth: 0.5)
            }
        }
    }

    private func drawPieces(in context: GraphicsContext, size: CGSize) {
        let squareSize = size.width / 8.0
        let transformer = BoardCoordinateTransformer(isFlipped: viewModel.isBoardFlipped)

        for (rankIndex, rank) in transformer.rankLabels.enumerated() {
            for (fileIndex, file) in transformer.fileLabels.enumerated() {
                let square = Square("\(file)\(rank)")

                if let piece = viewModel.position.piece(at: square) {
                    let rect = CGRect(
                        x: CGFloat(fileIndex) * squareSize + 4,
                        y: CGFloat(rankIndex) * squareSize + 4,
                        width: squareSize - 8,
                        height: squareSize - 8
                    )

                    if let image = NSImage(named: pieceImageName(for: piece)) {
                        context.draw(Image(nsImage: image), in: rect)
                    }
                }
            }
        }
    }

    // MARK: - Canvas Interaction Methods

    private func handleTap(at location: CGPoint, size: CGSize) {
        if let tappedSquare = square(at: location, size: size) {
            viewModel.handleSquareTap(tappedSquare)
        }
    }

    private func handleRightClick(at location: CGPoint, size: CGSize) {
        if let tappedSquare = square(at: location, size: size) {
            viewModel.handleRightClick(tappedSquare)
        }
    }

    private func square(at point: CGPoint, size: CGSize) -> Square? {
        let squareSize = size.width / 8.0
        let transformer = BoardCoordinateTransformer(isFlipped: viewModel.isBoardFlipped)

        // Check if the point is within the board bounds
        guard CGRect(origin: .zero, size: size).contains(point) else {
            return nil
        }

        let fileIndex = Int(point.x / squareSize)
        // Fix coordinate calculation: SwiftUI y coordinates are top-to-bottom,
        // but chess ranks are bottom-to-top, so we need to flip the y coordinate
        let rankIndex = Int((size.height - point.y) / squareSize)

        // Ensure indices are within valid range
        guard fileIndex >= 0 && fileIndex < 8 && rankIndex >= 0 && rankIndex < 8 else {
            return nil
        }

        let file = transformer.fileLabels[fileIndex]
        let rank = transformer.rankLabels[rankIndex]

        return Square("\(file)\(rank)")
    }



    private func coordinateLabelsView(squareSize: CGFloat) -> some View {
        ZStack {
            let rankArray = viewModel.isBoardFlipped ? [1, 2, 3, 4, 5, 6, 7, 8] : [8, 7, 6, 5, 4, 3, 2, 1]
            VStack(spacing: 0) {
                ForEach(rankArray, id: \.self) { rank in
                    HStack {
                        Text("\(rank)")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                        Spacer()
                    }
                    .frame(height: squareSize)
                }
            }
            .padding(.leading, 2)

            VStack {
                Spacer()
                HStack(spacing: 0) {
                    ForEach(viewModel.isBoardFlipped ? ["h", "g", "f", "e", "d", "c", "b", "a"] : ["a", "b", "c", "d", "e", "f", "g", "h"], id: \.self) { file in
                        VStack {
                            Spacer()
                            Text(file)
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                        .frame(width: squareSize)
                    }
                }
                .padding(.bottom, 0)
            }
        }
    }

    // MARK: - Piece Selection View

    private var pieceSelectionView: some View {
        VStack(spacing: 8) {
            Text("Pieces")
                .font(.headline)
                .padding(.bottom, 8)

            VStack(spacing: 4) {
                Text("White")
                    .font(.caption)
                    .foregroundColor(.secondary)

                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 4) {
                    ForEach(Piece.Kind.allCases.reversed(), id: \.self) { kind in
                        pieceButton(kind: kind, color: .white)
                    }
                }
            }

            Divider()

            VStack(spacing: 4) {
                Text("Black")
                    .font(.caption)
                    .foregroundColor(.secondary)

                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 4) {
                    ForEach(Piece.Kind.allCases.reversed(), id: \.self) { kind in
                        pieceButton(kind: kind, color: .black)
                    }
                }
            }

            Divider()

            Button(action: {
                viewModel.selectedTool = .delete
            }) {
                VStack {
                    Image(systemName: "trash")
                        .font(.title2)
                    Text("Delete")
                        .font(.caption)
                }
                .frame(maxWidth: .infinity)
                .padding(8)
                .background(viewModel.selectedTool == .delete ? Color.accentColor.opacity(0.3) : Color.clear)
                .cornerRadius(4)
            }
            .buttonStyle(PlainButtonStyle())

            Divider()

            HStack(spacing: 4) {
                Button(action: {
                    captureBoardToClipboard()
                }) {
                    VStack {
                        Image(systemName: "camera")
                            .font(.title3)
                        Text("Copy Screenshot")
                            .font(.caption2)
                            .multilineTextAlignment(.center)
                            .lineLimit(nil)
                    }
                    .frame(maxWidth: .infinity, minHeight: 50)
                    .padding(4)
                    .cornerRadius(4)
                }
                .buttonStyle(PlainButtonStyle())

                Button(action: {
                    saveBoardScreenshot()
                }) {
                    VStack {
                        Image(systemName: "square.and.arrow.down")
                            .font(.title3)
                        Text("Save Screenshot")
                            .font(.caption2)
                            .multilineTextAlignment(.center)
                            .lineLimit(nil)
                    }
                    .frame(maxWidth: .infinity, minHeight: 50)
                    .padding(4)
                    .cornerRadius(4)
                }
                .buttonStyle(PlainButtonStyle())
            }

            Spacer()
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
        .cornerRadius(8)
    }

    private func pieceButton(kind: Piece.Kind, color: Piece.Color) -> some View {
        let piece = Piece(kind, color: color, square: .a1)
        let isSelected = viewModel.selectedTool == .piece(piece)

        return Button(action: {
            viewModel.selectedTool = .piece(piece)
        }) {
            Image(pieceImageName(for: piece))
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 50, height: 50)
                .background(isSelected ? Color.accentColor.opacity(0.3) : Color.clear)
                .cornerRadius(4)
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Options View 1

    private var optionsView1: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Position Settings")
                .font(.headline)

            GroupBox(label: Text("Castling Rights")) {
                VStack(alignment: .leading, spacing: 4) {
                    Toggle("White King-side", isOn: $viewModel.whiteCanCastleKingSide)
                    Toggle("White Queen-side", isOn: $viewModel.whiteCanCastleQueenSide)
                    Toggle("Black King-side", isOn: $viewModel.blackCanCastleKingSide)
                    Toggle("Black Queen-side", isOn: $viewModel.blackCanCastleQueenSide)
                }
                .font(.caption)
            }

            GroupBox(label: Text("Side to Move")) {
                Picker("", selection: $viewModel.sideToMove) {
                    Text("White").tag(Piece.Color.white)
                    Text("Black").tag(Piece.Color.black)
                }
                .pickerStyle(SegmentedPickerStyle())
            }

            GroupBox(label: Text("Initial Move Number")) {
                HStack {
                    Button("-") {
                        if viewModel.initialNumber > 1 {
                            viewModel.initialNumber -= 1
                        }
                    }
                    .disabled(viewModel.initialNumber <= 1)

                    TextField("", value: $viewModel.initialNumber, format: .number)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .frame(width: 60)
                        .multilineTextAlignment(.center)
                        .onSubmit {
                            if viewModel.initialNumber < 1 {
                                viewModel.initialNumber = 1
                            }
                        }

                    Button("+") {
                        viewModel.initialNumber += 1
                    }
                }
                .font(.caption)
            }

            GroupBox(label: Text("Board Orientation")) {
                Toggle("Flip Board", isOn: $viewModel.isBoardFlipped)
                    .font(.caption)
            }

            GroupBox(label: Text("Clipboard")) {
                VStack(spacing: 4) {
                    Button("Copy FEN") {
                        viewModel.copyFENToClipboard()
                    }
                    Button("Paste FEN") {
                        viewModel.pasteFENFromClipboard()
                    }
                }
                .font(.caption)
            }

            GroupBox(label: fenLabel) {
                VStack(alignment: .leading, spacing: 4) {
                    TextEditor(text: $viewModel.fenString)
                        .font(.system(.caption, design: .monospaced))
                        .frame(minHeight: 50, maxHeight: 80)
                        .border(Color.gray.opacity(0.3), width: 1)
                        .cornerRadius(4)

                    Button("Load FEN") {
                        viewModel.loadFromFEN()
                    }
                    .font(.caption)
                }
            }

            Spacer()
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
        .cornerRadius(8)
    }

    private var optionsView2: some View {
        HStack(spacing: 16) {
            Button("OK") {
                if viewModel.validateCurrentFEN() {
                    if let position = viewModel.currentPosition {
                        onPositionSet?(position, viewModel.isBoardFlipped)
                    }
                    dismiss()
                } else {
                    showInvalidPositionAlert = true
                }
            }
            .keyboardShortcut(.return)

            Button("Reset") {
                viewModel.resetToStandardPosition()
            }

            Button("Clear") {
                viewModel.clearBoard()
            }

            Button("Cancel") {
                dismiss()
            }
            .keyboardShortcut(.escape)
        }
        .padding()
    }

    private var fenLabel: some View {
        HStack(spacing: 4) {
            Text("FEN")
            if !viewModel.isCurrentFENValid {
                Text("(Invalid)").foregroundColor(.red.opacity(0.8))
            }
        }
    }

    private func pieceImageName(for piece: Piece) -> String {
        let colorPrefix = piece.color == .white ? "w" : "b"
        let pieceSymbol: String

        switch piece.kind {
        case .pawn: pieceSymbol = "P"
        case .knight: pieceSymbol = "N"
        case .bishop: pieceSymbol = "B"
        case .rook: pieceSymbol = "R"
        case .queen: pieceSymbol = "Q"
        case .king: pieceSymbol = "K"
        }

        return "\(colorPrefix)\(pieceSymbol)"
    }
    
    // MARK: - Screenshot Methods
    
    private func captureBoardToClipboard() {
        Task { @MainActor in
            guard let image = captureChessBoard() else {
                print("Failed to capture chess board image")
                return
            }
            
            copyImageToClipboard(image)
        }
    }
    
    private func saveBoardScreenshot() {
        Task { @MainActor in
            guard let image = captureChessBoard() else {
                print("Failed to capture chess board image")
                return
            }
            
            saveImageToFile(image)
        }
    }
    
    @MainActor
    private func captureChessBoard() -> NSImage? {
        let screenshotView = createBoardForScreenshot()
        
        let screenshotWithBackground = VStack {
            screenshotView
        }
        .background(
            Rectangle()
                .fill(Color(.white.opacity(0.9)))
        )
        .padding(10)
        
        let renderer = ImageRenderer(content: screenshotWithBackground)
        renderer.scale = 2.0
        
        return renderer.nsImage
    }
    
    private func createBoardForScreenshot() -> some View {
        let squareSize = boardSize / 8
        let transformer = BoardCoordinateTransformer(isFlipped: viewModel.isBoardFlipped)
        
        return VStack(spacing: 0) {
            ForEach(transformer.rankLabels, id: \.self) { rank in
                HStack(spacing: 0) {
                    ForEach(transformer.fileLabels, id: \.self) { file in
                        let square = Square("\(file)\(rank)")
                        let isLightSquare = (square.file.number + square.rank.value) % 2 == 1
                        let backgroundColor = isLightSquare ? Color.brown.opacity(0.3) : Color.brown.opacity(0.7)
                        
                        ZStack {
                            Rectangle()
                                .fill(backgroundColor)
                            
                            if let piece = viewModel.position.piece(at: square) {
                                Image(pieceImageName(for: piece))
                                    .resizable()
                                    .aspectRatio(contentMode: .fit)
                                    .padding(4)
                            }
                        }
                        .frame(width: squareSize, height: squareSize)
                    }
                }
            }
        }
    }
    
    private func copyImageToClipboard(_ image: NSImage) {
        let pasteboard = NSPasteboard.general
        pasteboard.clearContents()
        pasteboard.writeObjects([image])
        
        print("Position editor chess board screenshot copied to clipboard")
    }
    
    private func saveImageToFile(_ image: NSImage) {
        let savePanel = NSSavePanel()
        savePanel.allowedContentTypes = [.png, .jpeg]
        
        let timestamp = Int(Date().timeIntervalSince1970)
        savePanel.nameFieldStringValue = "PositionEditor_\(timestamp).png"
        savePanel.title = "Save Position Editor Screenshot"
        savePanel.message = "Choose a location to save the position editor screenshot"
        
        savePanel.begin { response in
            guard response == .OK, let url = savePanel.url else { return }
            
            guard let tiffData = image.tiffRepresentation,
                  let bitmapImage = NSBitmapImageRep(data: tiffData),
                  let pngData = bitmapImage.representation(using: .png, properties: [:]) else {
                print("Failed to convert image to PNG data")
                return
            }
            
            do {
                try pngData.write(to: url)
                print("Position editor screenshot saved to: \(url.path)")
            } catch {
                print("Failed to save screenshot: \(error.localizedDescription)")
            }
        }
    }
}

// MARK: - Interactive Overlay View

struct InteractiveOverlayView: NSViewRepresentable {
    let onLeftClick: (CGPoint) -> Void
    let onRightClick: (CGPoint) -> Void

    func makeNSView(context: Context) -> NSView {
        let view = NSView()

        // Left click gesture
        let leftClick = NSClickGestureRecognizer(target: context.coordinator, action: #selector(Coordinator.handleLeftClick(_:)))
        leftClick.buttonMask = 0x1 // Left mouse button
        view.addGestureRecognizer(leftClick)

        // Right click gesture
        let rightClick = NSClickGestureRecognizer(target: context.coordinator, action: #selector(Coordinator.handleRightClick(_:)))
        rightClick.buttonMask = 0x2 // Right mouse button
        view.addGestureRecognizer(rightClick)

        return view
    }

    func updateNSView(_ nsView: NSView, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(onLeftClick: onLeftClick, onRightClick: onRightClick)
    }

    class Coordinator: NSObject {
        let onLeftClick: (CGPoint) -> Void
        let onRightClick: (CGPoint) -> Void

        init(onLeftClick: @escaping (CGPoint) -> Void, onRightClick: @escaping (CGPoint) -> Void) {
            self.onLeftClick = onLeftClick
            self.onRightClick = onRightClick
        }

        @objc func handleLeftClick(_ sender: NSClickGestureRecognizer) {
            let location = sender.location(in: sender.view)
            onLeftClick(location)
        }

        @objc func handleRightClick(_ sender: NSClickGestureRecognizer) {
            let location = sender.location(in: sender.view)
            onRightClick(location)
        }
    }
}

// MARK: - Right Click Recognizer View (Legacy - kept for compatibility)

struct RightClickGestureView: NSViewRepresentable {
    let onRightClick: (CGPoint) -> Void

    func makeNSView(context: Context) -> NSView {
        let view = NSView()
        let rightClick = NSClickGestureRecognizer(target: context.coordinator, action: #selector(Coordinator.handleRightClick(_:)))
        rightClick.buttonMask = 0x2 // Right mouse button
        view.addGestureRecognizer(rightClick)
        return view
    }

    func updateNSView(_ nsView: NSView, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(onRightClick: onRightClick)
    }

    class Coordinator: NSObject {
        let onRightClick: (CGPoint) -> Void

        init(onRightClick: @escaping (CGPoint) -> Void) {
            self.onRightClick = onRightClick
        }

        @objc func handleRightClick(_ sender: NSClickGestureRecognizer) {
            let location = sender.location(in: sender.view)
            onRightClick(location)
        }
    }
}
