//
//  PositionEditorViewModel.swift
//  MacChessBase
//
//  Created by AI Assistant on 2025/7/7.
//

import SwiftUI
import ChessKit
import AppKit

/// ViewModel for managing position editing state
@MainActor
final class PositionEditorViewModel: ObservableObject {
    // MARK: - Published Properties
    
    /// FEN string representation of current position (this is the source of truth)
    @Published var fenString: String = Position.standard.fen {
        didSet {
            guard oldValue != fenString else { return }
            isCurrentFENValid = validateCurrentFEN()
        }
    }
    
    /// Whether the current FEN string is valid
    @Published var isCurrentFENValid: Bool = true
    
    /// Currently selected editing tool
    @Published var selectedTool: EditingTool = .piece(Piece(.pawn, color: .white, square: .a1))
    
    /// Castling rights (derived from FEN)
    @Published var whiteCanCastleKingSide: Bool = true {
        didSet {
            guard oldValue != whiteCanCastleKingSide else { return }
            updateCastlingInFEN()
        }
    }
    @Published var whiteCanCastleQueenSide: Bool = true {
        didSet {
            guard oldValue != whiteCanCastleQueenSide else { return }
            updateCastlingInFEN()
        }
    }
    @Published var blackCanCastleKingSide: Bool = true {
        didSet {
            guard oldValue != blackCanCastleKingSide else { return }
            updateCastlingInFEN()
        }
    }
    @Published var blackCanCastleQueenSide: Bool = true {
        didSet {
            guard oldValue != blackCanCastleQueenSide else { return }
            updateCastlingInFEN()
        }
    }
    
    /// Side to move (derived from FEN)
    @Published var sideToMove: Piece.Color = .white {
        didSet {
            guard oldValue != sideToMove else { return }
            updateSideToMoveInFEN()
        }
    }
    
    /// Initial move number (corresponds to fullmove number in FEN)
    @Published var initialNumber: Int = 1 {
        didSet {
            guard oldValue != initialNumber else { return }
            updateInitialNumberInFEN()
        }
    }
    
    /// Board flip state
    @Published var isBoardFlipped: Bool = false
    
    /// Current position (derived from FEN)
    @Published var position: Position = .standard
    
    // MARK: - Private Properties
    
    private var isUpdatingFromFEN = false // Prevent circular updates
    
    // MARK: - Types
    
    enum EditingTool: Equatable {
        case piece(Piece)
        case delete
        
        static func == (lhs: EditingTool, rhs: EditingTool) -> Bool {
            switch (lhs, rhs) {
            case (.delete, .delete):
                return true
            case let (.piece(piece1), .piece(piece2)):
                return piece1.kind == piece2.kind && piece1.color == piece2.color
            default:
                return false
            }
        }
    }
    
    // MARK: - Initialization

    init() {
        updateFromFEN()
    }

    init(fen: String) {
        fenString = fen
        updateFromFEN()
    }

    init(fen: String, isBoardFlipped: Bool) {
        fenString = fen
        self.isBoardFlipped = isBoardFlipped
        updateFromFEN()
    }
    
    // MARK: - Computed Properties
    
    /// Gets the current position from FEN
    var currentPosition: Position? {
        return Position(fen: fenString, forceValid: false)
    }
    
    // MARK: - FEN Parsing and Generation
    
    /// Updates all derived properties from the current FEN string
    private func updateFromFEN() {
        guard !isUpdatingFromFEN else { return }
        isUpdatingFromFEN = true
        defer { isUpdatingFromFEN = false }
        
        guard let newPosition = Position(fen: fenString, forceValid: false) else {
            return
        }
        
        position = newPosition
        sideToMove = newPosition.sideToMove
        
        // Update castling rights from position
        let castlings = newPosition.legalCastlings
        whiteCanCastleKingSide = castlings.contains(.wK)
        whiteCanCastleQueenSide = castlings.contains(.wQ)
        blackCanCastleKingSide = castlings.contains(.bK)
        blackCanCastleQueenSide = castlings.contains(.bQ)
        
        // Extract initial number from FEN
        let fenParts = fenString.components(separatedBy: " ")
        if fenParts.count >= 6, let fullmoveNumber = Int(fenParts[5]) {
            initialNumber = fullmoveNumber
        }
    }
    
    #if DEBUG
    internal func updateFromFENTest() {
        updateFromFEN()
    }
    #endif
    
    /// Validates the current FEN string
    func validateCurrentFEN() -> Bool {
        return FENValidator.isValidFEN(fenString)
    }
    
    /// Updates the castling part of FEN string
    private func updateCastlingInFEN() {
        guard !isUpdatingFromFEN else { return }
        
        var fenParts = fenString.components(separatedBy: " ")
        guard fenParts.count >= 6 else { return }
        
        // Build castling string
        var castlingString = ""
        if whiteCanCastleKingSide { castlingString += "K" }
        if whiteCanCastleQueenSide { castlingString += "Q" }
        if blackCanCastleKingSide { castlingString += "k" }
        if blackCanCastleQueenSide { castlingString += "q" }
        
        if castlingString.isEmpty {
            castlingString = "-"
        }
        
        fenParts[2] = castlingString
        fenString = fenParts.joined(separator: " ")
        updateFromFEN()
    }
    
    /// Updates the side to move part of FEN string
    private func updateSideToMoveInFEN() {
        guard !isUpdatingFromFEN else { return }
        
        var fenParts = fenString.components(separatedBy: " ")
        guard fenParts.count >= 6 else { return }
        
        fenParts[1] = sideToMove == .white ? "w" : "b"
        fenString = fenParts.joined(separator: " ")
        updateFromFEN()
    }
    
    /// Updates the initial number (fullmove number) part of FEN string
    private func updateInitialNumberInFEN() {
        guard !isUpdatingFromFEN else { return }
        
        var fenParts = fenString.components(separatedBy: " ")
        guard fenParts.count >= 6 else { return }
        
        fenParts[5] = "\(max(1, initialNumber))" // Ensure minimum value of 1
        fenString = fenParts.joined(separator: " ")
        updateFromFEN()
    }
    
    /// Updates piece placement in FEN string
    private func updatePiecePlacementInFEN(_ newPlacement: String) {
        var fenParts = fenString.components(separatedBy: " ")
        guard fenParts.count >= 6 else { return }
        
        fenParts[0] = newPlacement
        fenString = fenParts.joined(separator: " ")
        updateFromFEN()
    }
    
    // MARK: - Board Editing Actions
    
    /// Handles tapping on a square
    func handleSquareTap(_ square: Square) {
        switch selectedTool {
        case .piece(let piece):
            // Check if the selected piece is the same as the piece at the target square
            if let existingPiece = position.piece(at: square),
               existingPiece.kind == piece.kind && existingPiece.color == piece.color {
                // Same piece type and color, remove it
                removePieceAt(square)
            } else {
                // Different piece or empty square, place the piece
                placePiece(piece, at: square)
            }
        case .delete:
            removePieceAt(square)
        }
    }
    
    /// Handles right-click on a square
    func handleRightClick(_ square: Square) {
        print("rightClick")
        switch selectedTool {
        case .piece(let piece):
            // Create piece with opposite color
            let oppositePiece = Piece(piece.kind, color: piece.color.opposite, square: square)
            
            // Check if the opposite piece is the same as the piece at the target square
            if let existingPiece = position.piece(at: square),
               existingPiece.kind == oppositePiece.kind && existingPiece.color == oppositePiece.color {
                // Same piece type and color, remove it
                removePieceAt(square)
            } else {
                // Different piece or empty square, place the opposite piece
                placePiece(oppositePiece, at: square)
            }
        case .delete:
            // For delete tool, right-click is the same as left-click
            removePieceAt(square)
        }
    }
    
    /// Places a piece at the specified square by modifying FEN
    private func placePiece(_ piece: Piece, at square: Square) {
        print(piece.color)
        let newPlacement = modifyPiecePlacement(currentPlacement: getCurrentPiecePlacement()) { boardArray in
            let rank = 8 - square.rank.value + 1
            let file = square.file.number - 1
            boardArray[rank - 1][file] = piece.fen
        }
        updatePiecePlacementInFEN(newPlacement)
    }
    
    /// Removes piece at the specified square by modifying FEN
    private func removePieceAt(_ square: Square) {
        let newPlacement = modifyPiecePlacement(currentPlacement: getCurrentPiecePlacement()) { boardArray in
            let rank = 8 - square.rank.value + 1
            let file = square.file.number - 1
            boardArray[rank - 1][file] = ""
        }
        updatePiecePlacementInFEN(newPlacement)
    }
    
    /// Gets current piece placement from FEN
    private func getCurrentPiecePlacement() -> String {
        let fenParts = fenString.components(separatedBy: " ")
        return fenParts.count > 0 ? fenParts[0] : ""
    }
    
    /// Modifies piece placement using a 2D array representation
    private func modifyPiecePlacement(currentPlacement: String, modifier: (inout [[String]]) -> Void) -> String {
        // Convert FEN piece placement to 2D array
        var boardArray: [[String]] = Array(repeating: Array(repeating: "", count: 8), count: 8)
        
        let ranks = currentPlacement.components(separatedBy: "/")
        for (rankIndex, rankString) in ranks.enumerated() {
            var fileIndex = 0
            for char in rankString {
                if char.isNumber {
                    let emptySquares = Int(String(char)) ?? 0
                    fileIndex += emptySquares
                } else {
                    if fileIndex < 8 {
                        boardArray[rankIndex][fileIndex] = String(char)
                        fileIndex += 1
                    }
                }
            }
        }
        
        // Apply modification
        modifier(&boardArray)
        
        // Convert back to FEN piece placement
        return boardArrayToFEN(boardArray)
    }
    
    /// Converts 2D board array back to FEN piece placement string
    private func boardArrayToFEN(_ boardArray: [[String]]) -> String {
        var result: [String] = []
        
        for rank in boardArray {
            var rankString = ""
            var emptyCount = 0
            
            for square in rank {
                if square.isEmpty {
                    emptyCount += 1
                } else {
                    if emptyCount > 0 {
                        rankString += "\(emptyCount)"
                        emptyCount = 0
                    }
                    rankString += square
                }
            }
            
            if emptyCount > 0 {
                rankString += "\(emptyCount)"
            }
            
            result.append(rankString)
        }
        
        return result.joined(separator: "/")
    }
    
    // MARK: - Preset Actions
    
    /// Loads position from FEN string
    func loadFromFEN() {
        updateFromFEN()
    }
    
    /// Resets to standard chess starting position
    func resetToStandardPosition() {
        fenString = Position.standard.fen
        position = Position.standard
        initialNumber = 1
    }
    
    /// Clears the board (empty position)
    func clearBoard() {
        fenString = "8/8/8/8/8/8/8/8 w - - 0 1"
        position = Position(fen: fenString, forceValid: false)!
        initialNumber = 1
    }
    
    // MARK: - Clipboard Operations
    
    /// Copies current FEN to clipboard
    func copyFENToClipboard() {
        let pasteboard = NSPasteboard.general
        pasteboard.clearContents()
        pasteboard.setString(fenString, forType: .string)
    }
    
    /// Pastes FEN from clipboard and loads it
    func pasteFENFromClipboard() {
        let pasteboard = NSPasteboard.general
        if let fenFromClipboard = pasteboard.string(forType: .string) {
            fenString = fenFromClipboard
        }
    }
}
